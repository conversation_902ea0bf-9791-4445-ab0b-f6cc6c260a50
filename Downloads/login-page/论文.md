好的，这是您提供的研究论文的Markdown格式内容。

# 基于整数规划的停车场车位排布优化算法研究

**贺思远¹ 李飚¹**
1. 东南大学建筑学院; <EMAIL>

**He Si yuan¹ Li Biao¹**
1. School of Architecture, Southeast University; <EMAIL>

---

## Research on Optimization Algorithm of parking lot layout based on Integer Programming

**摘要**: 随着社会经济的持续发展, 城市停车问题日益突显, 停车场和地下车库作为集中性的停车场所, 对改善城市停车问题具有重大意义。目前的车位排布设计, 主要依赖设计人员的经验进行人工排布, 而现有的车位排布算法, 无法有效处理复杂轮廓和内部障碍物的影响, 也无法协调多种车型和车位方向的组合排布。本研究以露天停车场为研究对象, 建立整数规划模型进行车位排布的优化设计。
研究首先建立了符合规范的车位排布数理模型, 然后优化区域划分以获取若干规则的可排布子区域。每个区域内, 根据不同车型, 角度和方向设定模板, 设置约束进行优化求解。本研究提出的优化算法精确性更高, 并且可以根据具体功能需要协调多种车位 (大小、角度、方向) 的个数和停车区域。
**关键词**: 停车场; 整数规划; 车位排布; 区域划分

**Abstract**: With the sustainable development of social economy, the problem of urban parking has become increasingly prominent. As centralized parking places, parking lots and underground garages are of great significance to solve the problem of urban parking. At present, the layout design of parking spaces mainly depends on the experience of designers to manual layout. However, the existing parking space layout algorithm can not effectively deal with the impact of complex contours and internal obstacles, nor can it coordinate the combined layout of multiple vehicle types and station directions. Take the open-air parking lot as research object, an integer programming model is established to optimize the layout of parking spaces.
Firstly, the mathematical model of parking space arrangement is established. Then, the segmented site is optimized to obtain several regular arrangeable sub regions. In each region, templates and constraints are set according to different vehicle types, angles and directions for optimal solution. The optimization algorithm proposed in this study has higher accuracy, and can coordinate the number of multiple parking spaces (size, angle, direction) and parking areas according to the specific functional needs.
**Keywords**: Parking Lot; Integer Programming; Parking Place Arrangements

---

## 1 引言

### 1.1 研究背景
随着改革开放的不断深入, 近年来我国经济实力持续上升, 汽车产业蓬勃发展, 迅速增长的汽车保有量给城市的交通管理带来了巨大的挑战; 同时市政建设中停车设施规划不到位, 停车设施增长滞后, 城市停车问题日益突显。根据国家发改委公布的数据显示, 我国大城市小型汽车与停车位之比仅为1:0.8, 停车位缺口仍在不断扩大。

建造室外停车场和地下车库是现阶段解决停车位缺口问题的行之有效的方式之一。然而, 相较于地面多层车库, 地下车库施工周期较长, 建造投入成本高。室外停车场较前两者更加经济, 而室外停车场占用面积较大, 一定程度上加重城市中的用地紧张问题。所以如何优化车位排布提高室外停车场的空间利用率, 得到经济高效的车位设计方案, 成为停车规划研究的探索方向之一。

现阶段的车位排布设计都是通过设计师手动比较少量方案完成的, 这一过程对于设计师自身的经验依赖程度较高。本文通过算法对实现地下车库车位的自动化排布进行探索与研究。

### 1.2 算法选择
整数规划属于数学规划的一类问题, 是关于整数变量的最优化问题, 即最大化或最小化一个全部或部分变量为整数的多元函数受约束于若干等式和不等式条件的最优化问题, 是应用最广泛的最优化方法之一。[1]

整数规划的基本要素可以分为:
1) 决策变量, 通过对目标的影响因素判断得出的所研究问题要求解的未知量, 以n维向量表示
2) 目标函数: 由决策变量和所要达到目的之间的函数关系得出, 记为f(X)。
3) 约束条件: 由所研究问题对决策变量X的限制条件给出, X∈D,D为可行域。D常用一组关于决策变量X的等数或不等式来界定, 分别称为等式约束和不等式约束。

### 1.3 既有研究
现有的车位排布问题研究主要分为三类: 一是定性推导车位排布参数与停车区域尺寸的关系模型。二是建立混合整数规划模型, 使用求解器求解; 三是设计启发式算法, 结合多阶段动态规划等算法进行求解[3]。
既有研究主要集中在: ①对垂直停车位的研究, 并未考虑到多角度的车位组合; ②研究小型汽车车位, 对于多种车型车位组合的研究并不充分[2][3], 而室外停车场恰恰需要考虑多角度多种类的车型组合优化的方法。本文提出了一种基于整数规划的多种类多角度车位组合优化方法, 立足于 Java 平台建立室外停车场车位排布数学优化模型, 并利用 Gurobi 求解器进行求解。

## 2 规则与求解框架

### 2.1 规则提取
根据《汽车库、修车库、停车场设计防火规范》GB50067-2014和《车库建筑设计规范》JGJ 100-2015, 停车场车位排布问题主要受到以下几种约束:

(1) 规范性尺寸约束
一些必须遵守的数值约束, 比如单向行驶的机动车道宽度不应小于 4m, 双向行驶的小型车道不应小于 6m, 双向行驶的中型车以上车道不应小于 7m。以及最小转弯半径约束 (表 1)、选择的车位排布角度 (图 1)。

**表1 机动车最小转弯半径**
| 车型 | 最小转弯半径 r₁(m) |
| :--- | :--- |
| 微型车 | 4.5 |
| 小型车 | 6.0 |
| 轻型车 | 6.0~7.0 |
| 中型车 | 7.2~9.0 |
| 大型车 | 9.0~15.0 |


**图1 车位排布角度**
(a:水平; b:垂直; c:30°斜列; d:45°斜列; e:60°斜列)

(2) 位置约束
车道车位必须在待布区域轮廓内。

(3) 不重叠约束
车道车位彼此不重叠, 且车道和车位不与区域内障碍物重叠。

(4) 车道相邻和连通性约束
满足车辆能够安全开入开出车位, 是车位正常使用的必要条件, 所以每个车位需要有与之相连的、宽度足够的车道。此外, 车道必须与出入口相连, 形成一个连通的网络。

### 2.2 求解框架
结合前一小节提取的规则, 本文设计了一种三阶段算法, 该算法可以处理复杂正交轮廓和障碍物对车位排布的影响, 具体步骤描述如下:

- **阶段1**: 依据基本约束对沿着外轮廓的外圈车位进行排布, 主要利用几何规则进行, 不涉及数学优化。
- **阶段2**: 利用整数规划进行区域划分优化, 将场地划分为若干可进行车位排布的矩形子区域。
- **阶段3**: 依据提取的规则, 设置变量和车位模板, 输入约束条件, 进行单区域排布优化。

上述框架中, 第三阶段为车位排布的主要步骤, 需要将第一、二阶段得出的区域与第三阶段单区域排布结合, 可视化最终车位排布结果。

## 3 停车场车位排布数学模型

### 3.1 外圈车位排布


**左: 图2 外圈车位排布示意**
**右: 图3 不重叠规则(a:凸点 b:凹点)(图片来源:作者自绘)**

外圈车位排布问题可以抽象简化为在 (可带洞) 正交多边形中, 沿着外轮廓紧密放置车位模板。空出车道距离 (缓冲区), 如图2所示, 得到的内部区域B即下一步区域划分的基础图形。

所以外圈车位排布的目标是需要占据尽量小的面积, 而同时能够容纳更多的车位数量。故选用最小的车位模板, 即小型车车位 (2.5*5.5m) 垂直于每一条边进行排布。

外圈车位排布基本逻辑是阵列放置, 但是需要注意的是不重叠的规则。笔者发现, 对于每个顶点来说, 顶点的凹凸性, 对于在顶点处的车位模板布置有影响: 凸点两侧都需要避让 (图 3a), 而对于凹点, 为使车位更多, 某一边车位可以出头覆盖另一边的模板宽度 (图3b)。基于表2的规则, 对外圈车位进行几何阵列的排布, 同时得到内部多边形进行下一步的区域划分。

**表2 基于顶点凹凸性的车位计算长度**
| 起点凹凸性 | 终点凹凸性 | 边的计算长度 |
| :--- | :--- | :--- |
| 凸 | 凸 | Length-2*d |
| 凸 | 凹 | Length |
| 凹 | 凸 | Length-d |
| 凹 | 凹 | Length+d |
**注**: Length 为该边长度, d 为车位长度, 此处取小型车车位 5. 5m

另外, 将出入口考虑进外圈车位排布, 在相应位置空出入口车道宽度。

### 3.2 区域划分
**问题转化**
区域划分问题可以抽象简化为在 (可带洞) 正交多边形中, 用数量一定 (n)、长w宽d、互不相交的矩形尽可能的占据多边形内部, 正交多边形均用矩形进行描述, 即一个大矩形减去一组负形矩形 (图4)。


**图4 复杂正交多边形的矩形描述**

**变量:**
$$
\begin{cases}
\min X \le x_i \le \max X \\
\min Y \le y_i \le \max Y \\
w_{min} \le w_i \le \max(w,d) \\
d_{min} \le d_i \le \min(w,d)
\end{cases} \quad(1)
$$
其中: $x_i, y_i$ 为矩形基准点横纵坐标, $w_i, d_i$ 为矩形长宽; $\min X, \max X$ 代表多边形横坐标范围。Y同理。

**目标函数:**
$$
\min E_{cover} = s - \sum_{i=1}^n w_i \times d_i \quad(2)
$$
最小化未被占据的面积, 式中: $s$ 为多边形面积。

**约束:**
1) 矩形在多边形的最小外接矩形内部:
$$
\begin{cases}
x_i + w_i \le \max X \\
y_i + d_i \le \max Y
\end{cases} \quad(3)
$$
2) 任意两个矩形i不重叠, 即矩形i位于矩形j的上/下/左/右任一侧。
$$
\begin{cases}
x_i + w_i \le x_j + M \cdot (1 - \sigma_{ij}^1) \\
x_j + w_j \le x_i + M \cdot (1 - \sigma_{ij}^2) \\
y_i + d_i \le y_j + M \cdot (1 - \sigma_{ij}^3) \\
y_j + d_j \le y_i + M \cdot (1 - \sigma_{ij}^4) \\
\sum_{k=1}^4 \sigma_{ij}^k \ge 1
\end{cases} \quad(4)
$$
式中 $\sigma$ 是一组描述或门(ORgate)的二元变量, M为取值尽量大的常数, 此处令 $M=w \times d$。

3) 矩形i和AABB的负形不重叠, 即矩形i位于负形矩形j的上/下/左/右任一侧。逻辑同上。

### 3.3 单区域车位排布
**问题转化**
给定长为W, 宽为L的场地, 车型C (小型车/中型车/大型车) 及其长宽为$w_c, h_c$, 设置M种车位, 其旋转角度 $\theta_j \in \{30, 45, 60, 90\}$。本文为了简化计算, 将一列或一条车位整体作为模板。同时为满足2.1中的车道连通性约束, 将车道也作为模板的一部分加入计算。因此本研究中的标准车道模板由双列车位夹着相应满足规范的车道组成 (图5)。通过计算在约束下模板的总数来表征该区域内车位排布。


**图5 模板及参数示意** (P₁, P₂: 第一, 二列投影长度; Wc, hc: 车位长宽; E₁, E₂, E₃: 各类模板宽度, W: 区域横向宽度, L: 区域纵向宽度, L': 内部模板的纵向总长约束, Aθj: 每个车位在纵向的计算宽度, 与该列车位数相乘受L'约束, θ: 车位角度)

**模板定义**
在场地中排布六大类车位模板:
$$ T_i \in \{ep_{vert}, e_{vert}, i_{vert}, ep_{hori}, e_{hori}, i_{hori}\} $$
其中: 1) 最外侧有车道的纵向双列车位模板为 $ep_{vert}$, 模板的横向长度根据旋转角度计算得出为 $E_{ep_{vert},\theta_j}$ (车位的投影长+车道的宽度, 下同理); 2) 内部有车道的纵向双列车位模板为 $i_{vert}$, 其横向长度为 $E_{i_{vert},\theta_j}$; 3) 最外侧有车道的纵向单列车位模板为 $e_{vert}$, 横向长度为 $E_{e_{vert},\theta_j}$; 4) 最外侧有车道的横向双列车位模板为 $ep_{hori}$, 纵向长度为 $E_{ep_{hori},\theta_j}$; 5) 最外侧有车道的横向单列车位模板为 $e_{hori}$, 纵向长度为 $E_{e_{hori},\theta_j}$; 6) 内部有车道的横向双列车位模板为 $i_{hori}$, 其横向长度为 $E_{i_{hori},\theta_j}$。

**目标函数**
使得场地中排布数量最多的车位
$$
\max \sum_{i=1}^6 \sum_{j=1}^M \sum_{k=1}^2 n(T_i, \theta_j)[k] X(T_i, \theta_j) \quad(5)
$$
$\forall T_i \in \{ep_{vert}, e_{vert}, i_{v}, ep_{hori}, e_{hori}, i_{h}\}, \forall j \in [1,M], \forall k \in [1,2]$
式中, $X(T_i, \theta_j)$为每种模板对应的列数; $n(T_i, \theta_j)[k]$为模板中每一列的车位数量:k代表第几列(一般有两列), k=1:第一列的车位数量, k=2:第二列的车位数量。当模板种类为$e_{vert}$时(贴外墙单列), 只有一列车位。即:
$n(e_{hori}, \theta_j)[2]=0$; $n(e_{vert}, \theta_j)[2]=0$

**约束条件**
1) 所有车位模板的长/宽和小于区域长/宽:
$$
\begin{cases}
\sum_{i=1}^6 \sum_{j=1}^M E_{T_i, \theta_j} X(T_i, \theta_j) \le W \\
\sum_{i=1}^6 \sum_{j=1}^M L_{T_i, \theta_j} X(T_i, \theta_j) \le L
\end{cases} \quad(6)
$$
式中, $E_{T_i, \theta_j}$为模板的宽度。$X(T_i, \theta_j)$为模板数量。

2) 贴外墙的车位模板不超过两个:
$$
\begin{cases}
\sum_{j=1}^M (X(ep_{vert}, \theta_j) + X(e_{vert}, \theta_j)) \le 2 \\
\sum_{j=1}^M (X(ep_{hori}, \theta_j) + X(e_{hori}, \theta_j)) \le 2
\end{cases} \quad(7)
$$

3) 模板中的每一列车位总长度不超过各自的长度限制$L'$。对每个模板的法向长度, 应用垂直方向模板的法向投影宽度进行约束, 保证纵向和横向模板不重叠。
$$
L'_h = L - \sum_{j=1}^M (X(ep_{hori}, \theta_j)E_{ep_{hori}, \theta_j} + X(e_{hori}, \theta_j)E_{e_{hori}, \theta_j}) \quad(8) \\
L'_w = W - \sum_{j=1}^M (X(ep_{vert}, \theta_j)E_{ep_{vert}, \theta_j} + X(e_{vert}, \theta_j)E_{e_{vert}, \theta_j})
$$
根据以上公式, 添加模板在投影方向长度约束:
$$
A_{\theta_j} n(T_i, \theta_j)[k] - L' \le 0 \quad(9)
$$
$\forall T_i \in \{ep_{vert}, e_{vert}, i_v, ep_{hori}, e_{hori}, i_h\}, \forall j \in [1,M], \forall k \in [1,2]$
式8上中X, E为纵向模板的数量和宽度, 式8下中X, E为横向模板的数量和宽度。式9中$A_{\theta_j}$同图5。

## 4 实例应用
为了更加直观的验证本文所提出的停车场车位排布算法的有效性, 本文对实例进行了轮廓建模和输入计算。图6a-d分别展示了三阶段流程的输出结果。

**图6 求解流程与分步结果(a:输入轮廓; b:外圈排布; c:区域划分; d:单区域车位排布)(图片来源:作者编写程序导出)**

在此基础上改变参数, 可以输出不同区域划分、不同车型比例控制下的车位排布结果。车型比例和计算结果以及计算时间的情况如图7和表3所示。
从表3中可以看出, 区域划分占据时间较多, 而作为算法主体的单区域车位排布可以极快找到最优解, 充分体现了算法的精确性和高效性。从图7中我们可以看出, 基于区域划分的排布方法可以将待排布地块中的大面积空白区域识别并分割排布, 但是对于某些复杂形体, 区域划分到单区域车位排布间的过渡, 因为出现小区域无法进行有效排布, 造成空间的浪费。


**图7 不同车型比例的排布结果(a, b, c, d 与表3第2到5行一一对应)(图片来源:作者编写程序导出)**

**表3 不同车型比例的计算时间(注:模板比例为模板个数比例)**
| 模板比例 | 计算结果 | 外圈车位 | 小型车 | 中型车 | 大型车 | 区域划分用时 | 车位排布用时 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| 5:2:1 | 4272 | 987 | 2370 | 660 | 255 | 30min | 6s |
| 5:3:2 | 3831 | 987 | 1332 | 1282 | 230 | 30min | 5s |
| 10:5:1 | 4328 | 987 | 2402 | 756 | 183 | 40min | 6s |
| 2:1:1 | 4035 | 987 | 1870 | 801 | 377 | 35min | 6s |
| / | 5239 | 987 | 4252 | / | / | 30min | 5s |

## 5 总结与展望
本研究建立了一种外圈车位排布到区域划分到单区域车位排布的三阶段整数规划算法流程。将场地条件及车位类型比例输入建立的整数规划模型后, 得到较为合理的车位排布方案。算法输出结果与人工排布的方案相比, 仍有一些需要修正的地方, 但整体上实现了人工程度较大的部分工作, 提高了车位排布设计的效率。本研究主要解决了正交体系下区域划分和车位排布问题, 对于非正交轮廓的场地车位排布算法研究还需进行探索。另外, 区域划分占据求解的大部分时间, 并且求解效率较低, 可以相应的加入启发式算法, 缩小解空间, 提高求解效率。
另外, 地下车库也是解决城市停车问题的市政设施, 进一步的研究可以将算法拓展至有柱网的地下库车位排布优化。

---
**①资助项目情况**: 
1. 自然科学基金面上项目“以特征向量矩阵运算为导向的建筑空间组合与生成系统研究”(编号: 51978139)
2. 2022年度省碳达峰碳中和科技创新专项资金(第三批)项目“低碳未来建筑关键技术研究与工程示范”

---
## 参考文献
[1] 孙小玲, 李端, 整数规划[M].北京科学出版社, 2010: 1-2
[2] Akmal S. Abdelfatah and MahmoudA. Taha. Parking Capacity Optimization Using Linear Programming [J]. Journal of Traffic and Logistics Engineering Vol. 2, No. 3, September 2014:176-181
[3] 徐涵喆.基于区域划分的地下车库车位排布问题算法研究[D].上海交通大学,2019
[4] 利润,地下停车场车位自动化排布方法研究[D].华南理工大学,2020
[5] Malin Karlsson,Richard Petersson, Optimisation of Parking Layout--A Mixed Integer Linear Programming Formulation for Maximum Number of Parking Spots, Applicable for Evaluation of Autonomous Parking Benefits. Chalmers University of Technology. 201